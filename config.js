// 应用配置文件
const AppConfig = {
    // 应用基本信息
    app: {
        name: '微信Web版',
        version: '1.0.0',
        description: '现代化的微信Web应用'
    },

    // API配置
    api: {
        baseUrl: '',  // API基础URL，空字符串表示使用相对路径
        timeout: 10000,  // 请求超时时间（毫秒）
        retryCount: 3,   // 失败重试次数
        retryDelay: 1000 // 重试延迟（毫秒）
    },

    // 界面配置
    ui: {
        // 主题配置
        theme: {
            primaryColor: '#07c160',      // 主色调
            primaryHover: '#06ae56',      // 主色调悬停
            backgroundColor: '#f8f9fa',   // 背景色
            textPrimary: '#212529',       // 主要文字颜色
            textSecondary: '#6c757d'      // 次要文字颜色
        },

        // 动画配置
        animation: {
            enabled: true,                // 是否启用动画
            duration: 300,               // 默认动画时长（毫秒）
            easing: 'cubic-bezier(0.4, 0, 0.2, 1)'  // 缓动函数
        },

        // 布局配置
        layout: {
            maxWidth: '600px',           // 最大宽度
            borderRadius: '12px',        // 默认圆角
            boxShadow: '0 8px 25px rgba(0, 0, 0, 0.12)'  // 默认阴影
        }
    },

    // 功能配置
    features: {
        // 聊天功能
        chat: {
            maxMessageLength: 1000,      // 最大消息长度
            autoScroll: true,            // 自动滚动到底部
            showTypingIndicator: true,   // 显示输入指示器
            enableEmoji: true,           // 启用表情功能
            enableMedia: true,           // 启用媒体消息
            messageRetry: true           // 启用消息重试
        },

        // 朋友圈功能
        moments: {
            maxContentLength: 2000,      // 最大内容长度
            maxImages: 9,                // 最大图片数量
            enableVideo: true,           // 启用视频功能
            autoRefresh: false,          // 自动刷新
            refreshInterval: 30000       // 刷新间隔（毫秒）
        },

        // 搜索功能
        search: {
            enabled: true,               // 启用搜索功能
            highlightResults: true,      // 高亮搜索结果
            maxResults: 100,             // 最大搜索结果数
            minSearchLength: 2           // 最小搜索长度
        },

        // 通知功能
        notifications: {
            enabled: true,               // 启用通知
            desktop: true,               // 桌面通知
            sound: false,                // 声音通知
            duration: 5000               // 通知显示时长（毫秒）
        },

        // 导出功能
        export: {
            enabled: true,               // 启用导出功能
            formats: ['txt', 'json'],    // 支持的格式
            includeMedia: false          // 是否包含媒体文件
        }
    },

    // 存储配置
    storage: {
        prefix: 'wechat_',             // 本地存储前缀
        expiry: 7 * 24 * 60 * 60 * 1000,  // 过期时间（7天）
        compress: false                 // 是否压缩存储数据
    },

    // 开发配置
    development: {
        debug: false,                   // 调试模式
        mockData: false,               // 使用模拟数据
        showPerformance: false,        // 显示性能信息
        logLevel: 'info'               // 日志级别：debug, info, warn, error
    },

    // 快捷键配置
    shortcuts: {
        enabled: true,                 // 启用快捷键
        showHint: true,               // 显示快捷键提示
        hintDelay: 3000,              // 提示延迟（毫秒）
        bindings: {
            search: 'Ctrl+f',         // 搜索
            export: 'Ctrl+e',         // 导出
            stats: 'Ctrl+i',          // 统计
            close: 'Escape'            // 关闭
        }
    },

    // 媒体配置
    media: {
        // 图片配置
        image: {
            maxSize: 5 * 1024 * 1024,    // 最大文件大小（5MB）
            maxWidth: 1920,              // 最大宽度
            maxHeight: 1080,             // 最大高度
            quality: 0.8,                // 压缩质量
            formats: ['jpg', 'jpeg', 'png', 'gif', 'webp']
        },

        // 视频配置
        video: {
            maxSize: 500 * 1024 * 1024,  // 最大文件大小（500MB）
            maxDuration: 300,            // 最大时长（秒）
            formats: ['mp4', 'webm', 'ogg']
        }
    },

    // 安全配置
    security: {
        enableCSP: true,               // 启用内容安全策略
        sanitizeInput: true,           // 输入内容净化
        preventXSS: true,              // XSS防护
        sessionTimeout: 24 * 60 * 60 * 1000  // 会话超时（24小时）
    }
};

// 获取配置值的辅助函数
function getConfig(path, defaultValue = null) {
    const keys = path.split('.');
    let value = AppConfig;
    
    for (const key of keys) {
        if (value && typeof value === 'object' && key in value) {
            value = value[key];
        } else {
            return defaultValue;
        }
    }
    
    return value;
}

// 设置配置值的辅助函数
function setConfig(path, value) {
    const keys = path.split('.');
    const lastKey = keys.pop();
    let target = AppConfig;
    
    for (const key of keys) {
        if (!(key in target) || typeof target[key] !== 'object') {
            target[key] = {};
        }
        target = target[key];
    }
    
    target[lastKey] = value;
}

// 合并用户配置
function mergeConfig(userConfig) {
    function deepMerge(target, source) {
        for (const key in source) {
            if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
                if (!target[key] || typeof target[key] !== 'object') {
                    target[key] = {};
                }
                deepMerge(target[key], source[key]);
            } else {
                target[key] = source[key];
            }
        }
    }
    
    deepMerge(AppConfig, userConfig);
}

// 从本地存储加载用户配置
function loadUserConfig() {
    try {
        const userConfig = localStorage.getItem(getConfig('storage.prefix') + 'config');
        if (userConfig) {
            mergeConfig(JSON.parse(userConfig));
        }
    } catch (error) {
        console.warn('加载用户配置失败:', error);
    }
}

// 保存用户配置到本地存储
function saveUserConfig(config) {
    try {
        localStorage.setItem(getConfig('storage.prefix') + 'config', JSON.stringify(config));
    } catch (error) {
        console.warn('保存用户配置失败:', error);
    }
}

// 重置配置为默认值
function resetConfig() {
    localStorage.removeItem(getConfig('storage.prefix') + 'config');
    location.reload();
}

// 导出配置对象和函数
if (typeof module !== 'undefined' && module.exports) {
    // Node.js环境
    module.exports = {
        AppConfig,
        getConfig,
        setConfig,
        mergeConfig,
        loadUserConfig,
        saveUserConfig,
        resetConfig
    };
} else {
    // 浏览器环境
    window.AppConfig = AppConfig;
    window.getConfig = getConfig;
    window.setConfig = setConfig;
    window.mergeConfig = mergeConfig;
    window.loadUserConfig = loadUserConfig;
    window.saveUserConfig = saveUserConfig;
    window.resetConfig = resetConfig;
    
    // 页面加载时自动加载用户配置
    document.addEventListener('DOMContentLoaded', loadUserConfig);
}