/* 朋友圈页面使用全局变量，无需重复定义 */

/* 表情选择器基础样式 */
.emoji-picker {
    position: absolute;
    left: 0;
    top: 100%;
    width: 100%;
    min-height: 270px;
    height: auto;
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-xl);
    padding: 15px;
    display: none;
    z-index: 1000;
    overflow: visible;
    margin-top: 8px;
    opacity: 0;
    transition: opacity var(--transition-normal);
}

.emoji-picker.active {
    display: block;
    opacity: 1;
}

/* 朋友圈页面的表情选择器样式 */
#post-emoji-picker {
    position: absolute;
    left: 0;
    top: 100%;
    width: 100%;
    min-width: unset;
    max-width: unset;
    min-height: 270px;
    height: auto;
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-xl);
    padding: 15px;
    display: none;
    z-index: 1000 !important;
    overflow: visible;
    margin-top: 8px;
    opacity: 0;
    transition: opacity var(--transition-normal);
    pointer-events: auto !important;
}

#post-emoji-picker.active {
    display: block;
    opacity: 1;
}

/* 用户资料区域 - 大厂风格优化 */
.user-profile {
    position: relative;
    height: 400px; /* 增加高度，延伸到箭头指向位置 */
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 0.9) 100%);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    box-shadow:
        0 20px 40px rgba(0, 0, 0, 0.1),
        0 8px 16px rgba(0, 0, 0, 0.08),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
    border-radius: 0 0 var(--border-radius-lg) var(--border-radius-lg);
    overflow: hidden;
    border: 1px solid rgba(255, 255, 255, 0.2);
    animation: slideInFromTop 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 页面加载动画 */
@keyframes slideInFromTop {
    0% {
        transform: translateY(-100px);
        opacity: 0;
    }
    100% {
        transform: translateY(0);
        opacity: 1;
    }
}

.cover-wrapper {
    position: relative;
    height: 300px; /* 增加封面高度 */
    overflow: hidden;
    cursor: pointer;
    border-radius: 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.cover-image {
    height: 100%;
    background-image: url('https://picsum.photos/600/400');
    background-size: cover;
    background-position: center;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1;
    transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
    filter: brightness(1.1) contrast(1.05) saturate(1.1);
}

.cover-wrapper:hover .cover-image {
    transform: scale(1.08);
    filter: brightness(1.2) contrast(1.1) saturate(1.2);
}

/* 添加渐变遮罩层，增强视觉效果 */
.cover-wrapper::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
        180deg,
        rgba(0, 0, 0, 0) 0%,
        rgba(0, 0, 0, 0.1) 50%,
        rgba(0, 0, 0, 0.3) 100%
    );
    z-index: 2;
    pointer-events: none;
}

/* 添加光效 */
.cover-wrapper::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(
        circle at center,
        rgba(255, 255, 255, 0.1) 0%,
        transparent 70%
    );
    z-index: 3;
    pointer-events: none;
    opacity: 0;
    transition: opacity 0.6s ease;
}

.cover-wrapper:hover::after {
    opacity: 1;
}

/* 封面点击提示 */
.cover-wrapper .click-hint {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 12px 20px;
    border-radius: var(--border-radius-lg);
    font-size: 14px;
    font-weight: 500;
    opacity: 0;
    transition: all 0.3s ease;
    z-index: 10;
    pointer-events: none;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.cover-wrapper:hover .click-hint {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1.05);
}

.cover-video-container {
    height: 100%;
    width: 100%;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1;
    display: none;
    overflow: hidden;
    border-radius: 0;
}

.cover-video {
    width: 100%;
    height: 100%;
    object-fit: cover;
    filter: brightness(1.1) contrast(1.05) saturate(1.1);
    transition: filter 0.6s ease;
}

.cover-video-container:hover .cover-video {
    filter: brightness(1.2) contrast(1.1) saturate(1.2);
}

.profile-info {
    position: absolute;
    right: 20px;
    bottom: 20px;
    display: flex;
    align-items: center;
    cursor: pointer;
    z-index: 10;
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-radius: var(--border-radius-lg);
    padding: 8px 12px 8px 8px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.profile-info:hover {
    background: rgba(255, 255, 255, 0.25);
    transform: translateY(-2px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.avatar {
    width: 80px;
    height: 80px;
    border-radius: var(--border-radius-lg);
    background-color: #f8f9fa;
    margin-right: 16px;
    background-image: url('https://picsum.photos/100/100');
    background-size: cover;
    background-position: center;
    border: 4px solid rgba(255, 255, 255, 0.9);
    box-shadow:
        0 8px 32px rgba(0, 0, 0, 0.15),
        0 4px 16px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

/* 头像光效 */
.avatar::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(
        45deg,
        transparent 30%,
        rgba(255, 255, 255, 0.3) 50%,
        transparent 70%
    );
    transform: rotate(-45deg);
    transition: transform 0.6s ease;
    opacity: 0;
}

.profile-info:hover .avatar {
    transform: scale(1.08) translateY(-2px);
    box-shadow:
        0 16px 48px rgba(0, 0, 0, 0.2),
        0 8px 24px rgba(0, 0, 0, 0.15),
        inset 0 1px 0 rgba(255, 255, 255, 0.4);
    border-color: rgba(255, 255, 255, 1);
}

.profile-info:hover .avatar::before {
    opacity: 1;
    transform: rotate(-45deg) translateX(100%);
}

.username {
    font-size: 20px;
    font-weight: 700;
    color: #fff;
    text-shadow:
        0 2px 8px rgba(0, 0, 0, 0.3),
        0 1px 4px rgba(0, 0, 0, 0.2);
    margin: 0;
    letter-spacing: 0.5px;
    line-height: 1.2;
    background: linear-gradient(135deg, #fff 0%, rgba(255, 255, 255, 0.9) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
    transition: all 0.3s ease;
}

.profile-info:hover .username {
    transform: translateY(-1px);
    filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3));
}

/* 发布朋友圈表单 - 大厂风格优化 */
.post-form {
    padding: 32px 24px;
    margin: 20px 16px 16px; /* 增加顶部间距，移除重叠 */
    position: relative;
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(30px);
    -webkit-backdrop-filter: blur(30px);
    box-shadow:
        0 16px 40px rgba(0, 0, 0, 0.08),
        0 8px 20px rgba(0, 0, 0, 0.06),
        inset 0 1px 0 rgba(255, 255, 255, 0.4);
    border-radius: var(--border-radius-xl);
    border: 1px solid rgba(255, 255, 255, 0.3);
    transform: translateY(0); /* 移除向上偏移 */
    z-index: 5;
    animation: slideInFromBottom 0.8s cubic-bezier(0.4, 0, 0.2, 1) 0.2s both;
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* 添加顶部装饰线，增强分离感 */
.post-form::before {
    content: '';
    position: absolute;
    top: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 40px;
    height: 4px;
    background: linear-gradient(90deg, transparent, rgba(0, 0, 0, 0.1), transparent);
    border-radius: 2px;
}

/* 表单进入动画 */
@keyframes slideInFromBottom {
    0% {
        transform: translateY(50px);
        opacity: 0;
    }
    100% {
        transform: translateY(0);
        opacity: 1;
    }
}

#post-content {
    width: 100%;
    height: 140px;
    border: 2px solid rgba(0, 0, 0, 0.06);
    border-radius: var(--border-radius-lg);
    padding: 20px;
    margin-bottom: 24px;
    resize: none;
    font-size: 16px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    line-height: 1.7;
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    box-shadow:
        0 4px 20px rgba(0, 0, 0, 0.04),
        inset 0 1px 0 rgba(255, 255, 255, 0.6);
    color: var(--text-primary);
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

#post-content::placeholder {
    color: rgba(0, 0, 0, 0.4);
    font-weight: 400;
}

#post-content:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow:
        0 8px 32px rgba(7, 193, 96, 0.15),
        0 4px 16px rgba(7, 193, 96, 0.1),
        0 0 0 4px rgba(7, 193, 96, 0.08),
        inset 0 1px 0 rgba(255, 255, 255, 0.8);
    background: rgba(255, 255, 255, 0.95);
    transform: translateY(-2px) scale(1.01);
}

/* 预览区域 */
.preview-container {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 15px;
    gap: 8px;
}

.preview-item {
    position: relative;
    width: calc(33.333% - 6px);
    height: 0;
    padding-bottom: calc(33.333% - 6px);
    background-color: #eee;
    background-size: cover;
    background-position: center;
    border-radius: var(--border-radius-sm);
    overflow: hidden;
    transition: all var(--transition-fast);
    box-shadow: var(--shadow-sm);
}

.preview-item:hover {
    transform: scale(1.03);
    box-shadow: var(--shadow-md);
}

.preview-video {
    width: 100%;
    height: 100%;
    position: absolute;
    object-fit: cover;
    background-color: #000;
}

.remove-preview {
    position: absolute;
    top: 5px;
    right: 5px;
    width: 24px;
    height: 24px;
    background-color: rgba(0, 0, 0, 0.6);
    color: #fff;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 12px;
    z-index: 2;
    transition: all var(--transition-fast);
    backdrop-filter: blur(2px);
    -webkit-backdrop-filter: blur(2px);
}

.remove-preview:hover {
    background-color: var(--red);
    transform: scale(1.1);
}

.post-actions {
    display: flex !important;
    justify-content: space-between;
    align-items: center;
    position: relative;
    visibility: visible !important;
    opacity: 1 !important;
}

.media-actions {
    display: flex !important;
    gap: 16px;
    align-items: center;
    justify-content: flex-start;
    visibility: visible !important;
    opacity: 1 !important;
}

/* 媒体按钮 - 大厂风格设计 */
.media-btn {
    width: 56px !important;
    height: 56px !important;
    min-width: 56px !important;
    min-height: 56px !important;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 18px;
    display: flex !important;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: #495057;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow:
        0 3px 12px rgba(0, 0, 0, 0.08),
        0 1px 4px rgba(0, 0, 0, 0.12),
        inset 0 1px 0 rgba(255, 255, 255, 0.8);
    border: 1px solid rgba(255, 255, 255, 0.6);
    position: relative;
    overflow: hidden;
    text-decoration: none;
    flex-shrink: 0;
    visibility: visible !important;
    opacity: 1 !important;
}

.media-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0.2) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
    border-radius: 17px;
}

.media-btn:hover::before {
    opacity: 1;
}

.media-btn:hover {
    transform: translateY(-3px) scale(1.05);
    box-shadow:
        0 8px 28px rgba(0, 0, 0, 0.15),
        0 4px 12px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.9);
}

.media-btn:active {
    transform: translateY(-1px) scale(1.02);
    transition: all 0.1s ease;
}

/* 表情按钮特殊样式 - 现代化设计 */
.media-btn.emoji-btn {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    color: #666;
    border: 2px solid transparent;
    position: relative;
    overflow: hidden;
}

/* 添加渐变边框效果 */
.media-btn.emoji-btn::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(135deg,
        rgba(255, 193, 7, 0.3) 0%,
        rgba(255, 152, 0, 0.3) 25%,
        rgba(156, 39, 176, 0.3) 50%,
        rgba(63, 81, 181, 0.3) 75%,
        rgba(76, 175, 80, 0.3) 100%);
    border-radius: 14px;
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: -1;
}

.media-btn.emoji-btn:hover::before {
    opacity: 1;
}

.media-btn.emoji-btn:hover {
    background: linear-gradient(135deg, #fff8e1 0%, #ffecb3 100%);
    color: #f39c12;
    box-shadow:
        0 8px 28px rgba(243, 156, 18, 0.25),
        0 4px 12px rgba(0, 0, 0, 0.12),
        0 2px 6px rgba(0, 0, 0, 0.08),
        inset 0 1px 0 rgba(255, 255, 255, 0.9);
    transform: translateY(-3px) scale(1.08);
}

/* 添加图标动画 */
.media-btn.emoji-btn i {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.media-btn.emoji-btn:hover i {
    transform: rotate(15deg) scale(1.1);
}

/* 图片按钮特殊样式 */
.media-btn[title="上传图片"] {
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
    color: #1976d2;
    border: 1px solid rgba(25, 118, 210, 0.2);
}

.media-btn[title="上传图片"]:hover {
    background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
    color: #388e3c;
    box-shadow:
        0 8px 28px rgba(25, 118, 210, 0.25),
        0 4px 12px rgba(25, 118, 210, 0.15),
        inset 0 1px 0 rgba(255, 255, 255, 0.9);
}

/* 视频按钮特殊样式 */
.media-btn[title="上传视频"] {
    background: linear-gradient(135deg, #fce4ec 0%, #f8bbd9 100%);
    color: #c2185b;
    border: 1px solid rgba(194, 24, 91, 0.2);
}

.media-btn[title="上传视频"]:hover {
    background: linear-gradient(135deg, #f3e5f5 0%, #e1bee7 100%);
    color: #7b1fa2;
    box-shadow:
        0 8px 28px rgba(194, 24, 91, 0.25),
        0 4px 12px rgba(194, 24, 91, 0.15),
        inset 0 1px 0 rgba(255, 255, 255, 0.9);
}

/* 发布按钮 - 大厂风格 */
#publish-btn {
    background: linear-gradient(135deg, #07c160 0%, #06ad56 100%);
    color: #fff;
    border: none;
    border-radius: 16px;
    padding: 14px 32px;
    font-size: 16px;
    font-weight: 700;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow:
        0 4px 12px rgba(7, 193, 96, 0.3),
        0 2px 6px rgba(7, 193, 96, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    letter-spacing: 0.8px;
    position: relative;
    overflow: hidden;
}

#publish-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.1) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
    border-radius: 15px;
}

#publish-btn:hover::before {
    opacity: 1;
}

#publish-btn:hover {
    background: linear-gradient(135deg, #08d168 0%, #07c160 100%);
    transform: translateY(-4px) scale(1.05);
    box-shadow:
        0 8px 30px rgba(7, 193, 96, 0.4),
        0 4px 12px rgba(7, 193, 96, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

#publish-btn:active {
    transform: translateY(-2px) scale(1.02);
    transition: all 0.1s ease;
}

#publish-btn:disabled {
    background: linear-gradient(135deg, #9ed9b9 0%, #a8dcc2 100%);
    cursor: not-allowed;
    transform: none;
    box-shadow:
        0 2px 6px rgba(0, 0, 0, 0.08),
        0 1px 2px rgba(0, 0, 0, 0.12);
    opacity: 0.7;
}

#publish-btn:disabled::before {
    display: none;
}

/* 朋友圈列表 */
.moments-list {
    padding: 15px;
    padding-bottom: 50px;
    position: relative;
    z-index: 5;
}

/* 确保表情选择器显示时，朋友圈列表不会拦截点击 */
.post-form {
    position: relative;
    z-index: 10;
}

/* 朋友圈卡片 - 大厂风格重新设计 */
.moment-item {
    margin-bottom: 20px;
    padding: 0;
    border-radius: 24px;
    background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.98) 0%,
        rgba(248, 250, 252, 0.98) 100%);
    backdrop-filter: blur(30px);
    -webkit-backdrop-filter: blur(30px);
    box-shadow:
        0 8px 32px rgba(0, 0, 0, 0.06),
        0 4px 16px rgba(0, 0, 0, 0.04),
        0 2px 8px rgba(0, 0, 0, 0.02),
        inset 0 1px 0 rgba(255, 255, 255, 0.8);
    border: 1px solid rgba(226, 232, 240, 0.5);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
    position: relative;
}

/* 添加微妙的渐变边框效果 */
.moment-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg,
        rgba(59, 130, 246, 0.1) 0%,
        rgba(139, 92, 246, 0.1) 25%,
        rgba(236, 72, 153, 0.1) 50%,
        rgba(245, 158, 11, 0.1) 75%,
        rgba(34, 197, 94, 0.1) 100%);
    border-radius: 24px;
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
    z-index: 1;
}

.moment-item:hover {
    transform: translateY(-6px) scale(1.01);
    box-shadow:
        0 20px 48px rgba(0, 0, 0, 0.12),
        0 8px 24px rgba(0, 0, 0, 0.08),
        0 4px 12px rgba(0, 0, 0, 0.04),
        inset 0 1px 0 rgba(255, 255, 255, 0.9);
    border-color: rgba(59, 130, 246, 0.2);
}

.moment-item:hover::before {
    opacity: 0.6;
}

/* 朋友圈头部 - 现代化设计 */
.moment-header {
    display: flex;
    align-items: center;
    padding: 20px 24px 16px;
    position: relative;
    z-index: 2;
    background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.9) 0%,
        rgba(248, 250, 252, 0.9) 100%);
    border-bottom: 1px solid rgba(226, 232, 240, 0.6);
}

/* 朋友圈删除按钮 */
.moment-delete-btn {
    position: absolute;
    right: 16px;
    top: 50%;
    transform: translateY(-50%);
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: rgba(220, 53, 69, 0.1);
    border: 1px solid rgba(220, 53, 69, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    color: #dc3545;
    font-size: 14px;
    opacity: 0.7;
}

.moment-delete-btn:hover {
    background: rgba(220, 53, 69, 0.15);
    border-color: rgba(220, 53, 69, 0.3);
    opacity: 1;
    transform: translateY(-50%) scale(1.1);
    box-shadow: 0 4px 12px rgba(220, 53, 69, 0.2);
}

.moment-delete-btn:active {
    transform: translateY(-50%) scale(0.95);
}

/* 头像优化 - 现代化设计 */
.moment-avatar {
    width: 48px;
    height: 48px;
    border-radius: 16px;
    background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
    margin-right: 16px;
    background-size: cover;
    background-position: center;
    box-shadow:
        0 4px 12px rgba(0, 0, 0, 0.08),
        0 2px 6px rgba(0, 0, 0, 0.04),
        inset 0 1px 0 rgba(255, 255, 255, 0.9);
    border: 2px solid rgba(255, 255, 255, 0.9);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

/* 头像悬浮效果 */
.moment-avatar::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg,
        transparent 30%,
        rgba(59, 130, 246, 0.2) 50%,
        transparent 70%);
    transform: rotate(-45deg);
    transition: transform 0.6s ease;
    opacity: 0;
}

.moment-header:hover .moment-avatar {
    transform: scale(1.05);
    box-shadow:
        0 8px 20px rgba(0, 0, 0, 0.12),
        0 4px 10px rgba(0, 0, 0, 0.08),
        inset 0 1px 0 rgba(255, 255, 255, 0.95);
}

.moment-header:hover .moment-avatar::before {
    opacity: 1;
    transform: rotate(-45deg) translateX(100%);
}

/* 用户信息区域 */
.moment-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.moment-user {
    font-size: 15px;
    font-weight: 600;
    color: #1e293b;
    margin: 0;
    line-height: 1.4;
    letter-spacing: 0.2px;
    background: linear-gradient(135deg, #1e293b 0%, #475569 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    transition: all 0.3s ease;
}

.moment-time {
    font-size: 12px;
    color: #64748b;
    font-weight: 500;
    opacity: 0.8;
    transition: all 0.3s ease;
}

.moment-header:hover .moment-time {
    opacity: 1;
    color: #475569;
}

/* 朋友圈内容区域 - 优化排版 */
.moment-content {
    padding: 16px 24px 20px;
    font-size: 15px;
    line-height: 1.7;
    color: #334155;
    font-weight: 400;
    letter-spacing: 0.2px;
    position: relative;
    z-index: 2;
    background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.6) 0%,
        rgba(248, 250, 252, 0.6) 100%);
    word-wrap: break-word;
    word-break: break-word;
}

.moment-images {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
    padding: 0 16px 16px;
}

.moment-images.single {
    display: block;
    padding: 0 16px 16px;
}

.moment-images.dual {
    display: flex;
    gap: 4px;
    padding: 0 16px 16px;
}

.moment-image {
    flex: 1 0 auto;
    width: calc(33.333% - 3px);
    height: 0;
    padding-bottom: calc(33.333% - 3px);
    background-color: #eee;
    background-size: cover;
    background-position: center;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    border-radius: var(--border-radius-sm);
    transition: all var(--transition-fast);
}

.moment-images.single .moment-image {
    width: 100%;
    max-height: 400px;
    padding-bottom: 60%;
    border-radius: var(--border-radius-md);
}

.moment-images.dual .moment-image {
    width: calc(50% - 2px);
    padding-bottom: 50%;
}

.moment-image:hover {
    transform: scale(1.02);
    box-shadow: var(--shadow-sm);
}

.moment-video-container {
    width: calc(100% - 32px); /* 减去左右margin的32px */
    padding-bottom: 56.25%;
    position: relative;
    background-color: #000;
    cursor: pointer;
    overflow: hidden;
    border-radius: var(--border-radius-md);
    margin: 0 16px 16px;
    box-shadow: var(--shadow-sm);
    transition: all var(--transition-fast);
}

.moment-video-container:hover {
    transform: scale(1.02);
    box-shadow: var(--shadow-md);
}

.moment-video {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* 朋友圈动作区域 - 大厂风格重新设计 */
.moment-actions {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    gap: 12px;
    padding: 16px 20px;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.95) 100%);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-top: 1px solid rgba(226, 232, 240, 0.8);
    border-radius: 0 0 var(--border-radius-xl) var(--border-radius-xl);
    position: relative;
    overflow: hidden;
    flex-wrap: nowrap;
}

/* 添加微妙的光效 */
.moment-actions::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg,
        transparent 0%,
        rgba(59, 130, 246, 0.3) 25%,
        rgba(139, 92, 246, 0.3) 50%,
        rgba(236, 72, 153, 0.3) 75%,
        transparent 100%);
    opacity: 0.6;
}

/* 现代化图标按钮设计 - 只显示图标 */
.action-btn {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    padding: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.95) 100%);
    border: 1px solid rgba(226, 232, 240, 0.6);
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    flex-shrink: 0;
    box-shadow:
        0 2px 8px rgba(0, 0, 0, 0.06),
        0 1px 4px rgba(0, 0, 0, 0.04),
        inset 0 1px 0 rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
    overflow: hidden;
}

/* 按钮悬浮效果 */
.action-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg,
        rgba(59, 130, 246, 0.1) 0%,
        rgba(139, 92, 246, 0.1) 50%,
        rgba(236, 72, 153, 0.1) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
    border-radius: 19px;
}

.action-btn:hover::before {
    opacity: 1;
}

.action-btn:hover {
    transform: translateY(-3px) scale(1.1);
    border-color: rgba(59, 130, 246, 0.4);
    box-shadow:
        0 8px 20px rgba(59, 130, 246, 0.2),
        0 4px 12px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.95);
}

.action-btn:active {
    transform: translateY(-1px) scale(1.05);
    transition: all 0.1s ease;
}

/* 图标样式优化 - 只显示图标 */
.action-btn i {
    font-size: 18px;
    color: #64748b;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    flex-shrink: 0;
}

.action-btn:hover i {
    transform: scale(1.2);
    color: #3b82f6;
}

/* 点赞按钮特殊样式 */
.like-btn {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(254, 242, 242, 0.95) 100%);
    border-color: rgba(239, 68, 68, 0.3);
}

.like-btn i {
    color: #ef4444;
}

.like-btn:hover {
    border-color: rgba(239, 68, 68, 0.5);
    box-shadow:
        0 8px 20px rgba(239, 68, 68, 0.25),
        0 4px 12px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.95);
}

.like-btn:hover i {
    color: #dc2626;
    transform: scale(1.3);
}

.like-btn.liked {
    background: linear-gradient(135deg, rgba(239, 68, 68, 0.15) 0%, rgba(220, 38, 38, 0.15) 100%);
    border-color: rgba(239, 68, 68, 0.4);
}

.like-btn.liked i {
    color: #dc2626;
    animation: heartbeat 1.5s ease-in-out infinite;
}

.like-btn.liked::before {
    background: linear-gradient(135deg, rgba(239, 68, 68, 0.2) 0%, rgba(220, 38, 38, 0.2) 100%);
    opacity: 1;
}

.like-btn.liked:hover {
    transform: translateY(-3px) scale(1.15);
    box-shadow:
        0 10px 24px rgba(239, 68, 68, 0.3),
        0 5px 15px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.95);
}

.like-btn.liked {
    color: var(--red);
}

.like-btn.liked i {
    animation: likeAnimation 0.5s ease;
}

@keyframes likeAnimation {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.3); }
}

/* 社交反馈区域 - 现代化重新设计 */
.moment-social-feedback {
    background: linear-gradient(135deg,
        rgba(248, 250, 252, 0.9) 0%,
        rgba(241, 245, 249, 0.9) 100%);
    padding: 16px 24px;
    position: relative;
    border-top: 1px solid rgba(226, 232, 240, 0.6);
    z-index: 2;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

/* 移除旧的三角形指示器，使用更现代的设计 */
.moment-social-feedback::before {
    content: '';
    position: absolute;
    top: 0;
    left: 24px;
    right: 24px;
    height: 1px;
    background: linear-gradient(90deg,
        transparent 0%,
        rgba(59, 130, 246, 0.2) 25%,
        rgba(139, 92, 246, 0.2) 50%,
        rgba(236, 72, 153, 0.2) 75%,
        transparent 100%);
    z-index: 1;
}

/* 点赞区域 - 现代化设计 */
.moment-likes {
    padding: 8px 16px;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 6px;
    margin-bottom: 12px;
    background: linear-gradient(135deg,
        rgba(239, 68, 68, 0.05) 0%,
        rgba(220, 38, 38, 0.05) 100%);
    border-radius: 16px;
    border: 1px solid rgba(239, 68, 68, 0.1);
    font-size: 13px;
    font-weight: 500;
    color: #475569;
    transition: all 0.3s ease;
}

.moment-likes:hover {
    background: linear-gradient(135deg,
        rgba(239, 68, 68, 0.08) 0%,
        rgba(220, 38, 38, 0.08) 100%);
    border-color: rgba(239, 68, 68, 0.15);
    transform: translateY(-1px);
}

.moment-likes i {
    color: #ef4444;
    margin-right: 6px;
    font-size: 14px;
    animation: heartbeat 2s ease-in-out infinite;
}

/* 心跳动画 */
@keyframes heartbeat {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

/* 评论区域 - 现代化设计 */
.moment-comments {
    border-top: 1px solid rgba(226, 232, 240, 0.6);
    padding-top: 12px;
}

.comment-item {
    padding: 10px 16px;
    margin: 6px 0;
    position: relative;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border-radius: 12px;
    background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.6) 0%,
        rgba(248, 250, 252, 0.6) 100%);
    border: 1px solid rgba(226, 232, 240, 0.4);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.comment-item:hover {
    background: linear-gradient(135deg,
        rgba(59, 130, 246, 0.05) 0%,
        rgba(139, 92, 246, 0.05) 100%);
    border-color: rgba(59, 130, 246, 0.2);
    transform: translateX(4px) translateY(-1px);
    box-shadow:
        0 4px 12px rgba(59, 130, 246, 0.1),
        0 2px 6px rgba(0, 0, 0, 0.05);
}

.comment-item:not(:last-child) {
    margin-bottom: 8px;
}

/* 评论内容 - 现代化排版 */
.comment-content {
    display: flex;
    flex-wrap: wrap;
    align-items: baseline;
    font-size: 13px;
    line-height: 1.6;
    position: relative;
    gap: 2px;
}

.comment-user {
    color: #3b82f6;
    font-weight: 600;
    font-size: 13px;
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    transition: all 0.3s ease;
    cursor: pointer;
}

.comment-user:hover {
    transform: scale(1.02);
}

.reply-text {
    color: #64748b;
    margin: 0 4px;
    font-weight: 500;
    font-size: 12px;
}

.reply-target {
    color: #8b5cf6;
    font-weight: 600;
    font-size: 13px;
    background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    transition: all 0.3s ease;
    cursor: pointer;
}

.reply-target:hover {
    transform: scale(1.02);
}

.comment-text {
    color: #334155;
    font-weight: 400;
    flex: 1;
    word-wrap: break-word;
    word-break: break-word;
}

.comment-item-actions {
    display: none;
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
}

.comment-item:hover .comment-item-actions {
    display: block;
}

.comment-reply-btn {
    background: none;
    border: none;
    font-size: 12px;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 3px 8px;
    border-radius: 4px;
    transition: all var(--transition-fast);
    background-color: rgba(0, 0, 0, 0.05);
}

.comment-reply-btn:hover {
    color: var(--primary-color);
    background-color: var(--light-primary);
}

/* 评论弹窗 */
.comment-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at center, rgba(0, 0, 0, 0.4) 0%, rgba(0, 0, 0, 0.7) 100%);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 900;
    opacity: 0;
    transition: all var(--transition-normal);
}

.comment-modal.active {
    opacity: 1;
}

.comment-container {
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    width: 90%;
    max-width: 500px;
    border-radius: var(--border-radius-xl);
    padding: 24px;
    position: relative;
    z-index: 950;
    box-shadow: var(--shadow-xl);
    border: 1px solid rgba(255, 255, 255, 0.3);
    transform: translateY(20px) scale(0.95);
    transition: all var(--transition-normal);
}

.comment-modal.active .comment-container {
    transform: translateY(0) scale(1);
}

#moment-comment-content {
    width: 100%;
    height: 100px;
    border: 1px solid #eee;
    border-radius: 8px;
    padding: 12px;
    font-size: 16px;
    resize: none;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    position: relative;
    z-index: 1;
}

.comment-content-wrapper {
    position: relative;
    width: 100%;
    overflow: visible;
}

/* 评论表情选择器样式 */
#moment-comment-emoji-picker {
    position: absolute;
    left: 0;
    top: 100%;
    width: 100%;
    min-width: unset;
    max-width: unset;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 249, 250, 0.95) 100%);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 2px solid transparent;
    border-radius: 16px;
    box-shadow:
        0 8px 32px rgba(0, 0, 0, 0.12),
        0 4px 16px rgba(0, 0, 0, 0.08),
        0 2px 8px rgba(0, 0, 0, 0.04);
    padding: 0;
    display: none;
    z-index: 1001;
    overflow: visible;
    margin-top: 12px;
    opacity: 0;
    transition: opacity 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 添加渐变边框效果 */
#moment-comment-emoji-picker::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(135deg,
        rgba(255, 193, 7, 0.3) 0%,
        rgba(255, 152, 0, 0.3) 25%,
        rgba(156, 39, 176, 0.3) 50%,
        rgba(63, 81, 181, 0.3) 75%,
        rgba(76, 175, 80, 0.3) 100%);
    border-radius: 18px;
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: -1;
}

#moment-comment-emoji-picker.active {
    display: block !important;
    opacity: 1 !important;
}

#moment-comment-emoji-picker.active::before {
    opacity: 1 !important;
}

/* 移动端滚动指示器 */
@media (max-width: 768px) {
    #moment-comment-emoji-picker::after {
        content: '';
        position: absolute;
        bottom: 8px;
        left: 50%;
        transform: translateX(-50%);
        width: 30px;
        height: 3px;
        background: linear-gradient(90deg, transparent 0%, rgba(255, 193, 7, 0.6) 50%, transparent 100%);
        border-radius: 2px;
        opacity: 0;
        transition: opacity 0.3s ease;
        pointer-events: none;
        z-index: 10;
    }

    #moment-comment-emoji-picker.active::after {
        opacity: 1;
        animation: scrollHint 2s ease-in-out infinite;
    }
}

@keyframes scrollHint {
    0%, 100% { opacity: 0.6; transform: translateX(-50%) scale(1); }
    50% { opacity: 1; transform: translateX(-50%) scale(1.1); }
}

.emoji-container {
    display: grid;
    grid-template-columns: repeat(6, 1fr);
    gap: 12px;
    padding: 15px;
    max-height: 280px;
    overflow-y: auto;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border-radius: 12px;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* 自定义滚动条 - 桌面端 */
.emoji-container::-webkit-scrollbar {
    width: 8px;
}

.emoji-container::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.05);
    border-radius: 4px;
    margin: 4px 0;
}

.emoji-container::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #ffc107 0%, #ff9800 100%);
    border-radius: 4px;
    transition: all 0.3s ease;
    min-height: 30px;
}

.emoji-container::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #ffb300 0%, #f57c00 100%);
    width: 10px;
}

.emoji-container::-webkit-scrollbar-thumb:active {
    background: linear-gradient(135deg, #ff8f00 0%, #e65100 100%);
}

.emoji {
    width: 44px;
    height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 26px;
    cursor: pointer;
    border-radius: 12px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border: 2px solid transparent;
    position: relative;
    overflow: hidden;
    box-shadow:
        0 2px 8px rgba(0, 0, 0, 0.06),
        0 1px 3px rgba(0, 0, 0, 0.04);
}

/* 表情图标悬浮效果 */
.emoji::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(135deg,
        rgba(255, 193, 7, 0.4) 0%,
        rgba(255, 152, 0, 0.4) 50%,
        rgba(156, 39, 176, 0.4) 100%);
    border-radius: 14px;
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: -1;
}

.emoji:hover::before {
    opacity: 1;
}

.emoji:hover {
    background: linear-gradient(135deg, #fff8e1 0%, #ffecb3 100%);
    transform: translateY(-2px) scale(1.15);
    box-shadow:
        0 8px 20px rgba(243, 156, 18, 0.2),
        0 4px 12px rgba(0, 0, 0, 0.1),
        0 2px 6px rgba(0, 0, 0, 0.06);
}

.emoji:active {
    transform: translateY(0) scale(1.1);
    transition: all 0.1s ease;
}

/* 移动端触摸优化 */
@media (hover: none) and (pointer: coarse) {
    /* 移动设备专用样式 */
    .emoji {
        /* 移除hover效果，使用touch反馈 */
        -webkit-tap-highlight-color: rgba(255, 193, 7, 0.2);
        tap-highlight-color: rgba(255, 193, 7, 0.2);
    }

    .emoji:hover {
        /* 在触摸设备上禁用hover效果 */
        transform: none;
        background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
        box-shadow:
            0 2px 8px rgba(0, 0, 0, 0.06),
            0 1px 3px rgba(0, 0, 0, 0.04);
    }

    .emoji:active {
        /* 优化触摸反馈 */
        transform: scale(0.95);
        background: linear-gradient(135deg, #fff8e1 0%, #ffecb3 100%);
        transition: all 0.1s ease;
    }

    /* 表情选择器在触摸设备上的优化 */
    #moment-comment-emoji-picker {
        /* 确保在移动端有足够的触摸空间 */
        padding-bottom: 10px;
    }

    .emoji-container {
        /* 优化滚动体验 */
        -webkit-overflow-scrolling: touch;
        overflow-scrolling: touch;
    }
}

/* 回复信息区域 */
.reply-info {
    background-color: #f8f8f8;
    border-radius: 8px;
    padding: 10px 15px;
    margin-bottom: 15px;
    font-size: 14px;
    position: relative;
    display: flex;
    align-items: center;
}

#reply-user {
    color: #576b95;
    font-weight: 500;
    margin-left: 5px;
}

#moment-cancel-reply {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #999;
    cursor: pointer;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

#moment-cancel-reply:hover {
    background-color: #e5e5e5;
    color: #666;
}

.comment-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 15px;
}

/* 评论区表情按钮 - 现代化设计 */
.emoji-btn {
    width: 48px;
    height: 48px;
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 22px;
    color: #666;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    box-shadow:
        0 4px 12px rgba(0, 0, 0, 0.08),
        0 2px 4px rgba(0, 0, 0, 0.04),
        inset 0 1px 0 rgba(255, 255, 255, 0.9);
    border: 2px solid transparent;
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

/* 添加渐变边框效果 */
.emoji-btn::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(135deg,
        rgba(255, 193, 7, 0.3) 0%,
        rgba(255, 152, 0, 0.3) 25%,
        rgba(156, 39, 176, 0.3) 50%,
        rgba(63, 81, 181, 0.3) 75%,
        rgba(76, 175, 80, 0.3) 100%);
    border-radius: 18px;
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: -1;
}

/* 添加内部光晕效果 */
.emoji-btn::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: radial-gradient(circle, rgba(255, 193, 7, 0.4) 0%, transparent 70%);
    border-radius: 50%;
    opacity: 0;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    transform: translate(-50%, -50%);
    pointer-events: none;
}

.emoji-btn:hover::before {
    opacity: 1;
}

.emoji-btn:hover::after {
    width: 40px;
    height: 40px;
    opacity: 1;
}

.emoji-btn:hover {
    background: linear-gradient(135deg, #fff8e1 0%, #ffecb3 100%);
    color: #f39c12;
    transform: translateY(-3px) scale(1.08);
    box-shadow:
        0 8px 25px rgba(243, 156, 18, 0.25),
        0 4px 12px rgba(0, 0, 0, 0.12),
        0 2px 6px rgba(0, 0, 0, 0.08),
        inset 0 1px 0 rgba(255, 255, 255, 0.9);
}

.emoji-btn:active {
    transform: translateY(-1px) scale(1.04);
    transition: all 0.1s ease;
}

/* 添加图标动画 */
.emoji-btn i {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.emoji-btn:hover i {
    transform: rotate(15deg) scale(1.1);
}

.comment-btns {
    display: flex;
    gap: 10px;
}

/* 评论按钮 - 大厂风格 */
#moment-cancel-comment, #moment-submit-comment {
    padding: 10px 24px;
    border-radius: 12px;
    border: none;
    font-size: 15px;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    font-weight: 600;
    letter-spacing: 0.5px;
    position: relative;
    overflow: hidden;
}

#moment-cancel-comment {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    color: #6c757d;
    box-shadow:
        0 2px 6px rgba(0, 0, 0, 0.08),
        0 1px 2px rgba(0, 0, 0, 0.12),
        inset 0 1px 0 rgba(255, 255, 255, 0.8);
    border: 1px solid rgba(255, 255, 255, 0.6);
}

#moment-cancel-comment::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.6) 0%, rgba(255, 255, 255, 0.2) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
    border-radius: 11px;
}

#moment-cancel-comment:hover::before {
    opacity: 1;
}

#moment-cancel-comment:hover {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    color: #495057;
    transform: translateY(-2px);
    box-shadow:
        0 6px 20px rgba(0, 0, 0, 0.12),
        0 2px 6px rgba(0, 0, 0, 0.08),
        inset 0 1px 0 rgba(255, 255, 255, 0.9);
}

#moment-submit-comment {
    background: linear-gradient(135deg, #07c160 0%, #06ad56 100%);
    color: white;
    box-shadow:
        0 2px 8px rgba(7, 193, 96, 0.3),
        0 1px 3px rgba(7, 193, 96, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

#moment-submit-comment::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.1) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
    border-radius: 11px;
}

#moment-submit-comment:hover::before {
    opacity: 1;
}

#moment-submit-comment:hover {
    background: linear-gradient(135deg, #08d168 0%, #07c160 100%);
    transform: translateY(-2px);
    box-shadow:
        0 6px 25px rgba(7, 193, 96, 0.4),
        0 2px 8px rgba(7, 193, 96, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

#moment-submit-comment:active {
    transform: translateY(-1px);
    transition: all 0.1s ease;
}

#moment-submit-comment:disabled {
    background: linear-gradient(135deg, #9ed9b9 0%, #a8dcc2 100%);
    cursor: not-allowed;
    transform: none;
    box-shadow:
        0 2px 6px rgba(0, 0, 0, 0.08),
        0 1px 2px rgba(0, 0, 0, 0.12);
    opacity: 0.7;
}

#moment-submit-comment:disabled::before {
    display: none;
}

/* 封面更换弹窗样式 */
.cover-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.cover-container {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-radius: var(--border-radius-lg);
    padding: 24px;
    min-width: 320px;
    max-width: 400px;
    box-shadow: var(--shadow-xl);
    border: 1px solid rgba(255, 255, 255, 0.3);
    animation: fadeInUp 0.3s ease-out;
}

.cover-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 1px solid var(--medium-grey);
}

.cover-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.close-btn {
    background: none;
    border: none;
    font-size: 18px;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 8px;
    border-radius: 50%;
    transition: all var(--transition-fast);
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
}

.close-btn:hover {
    background: var(--medium-grey);
    color: var(--text-primary);
}

.cover-options {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.cover-option {
    width: 100%;
}

.cover-upload-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    width: 100%;
    padding: 16px 20px;
    background: var(--primary-gradient);
    color: white;
    border: none;
    border-radius: var(--border-radius-md);
    cursor: pointer;
    transition: all var(--transition-fast);
    font-size: 16px;
    font-weight: 500;
    text-decoration: none;
    box-shadow: var(--shadow-sm);
}

.cover-upload-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.cover-upload-btn i {
    font-size: 18px;
}

/* 响应式设计 - 大厂风格优化 */
@media (max-width: 768px) {
    .user-profile {
        height: 360px;
        margin: 0 8px;
        border-radius: 0 0 var(--border-radius-lg) var(--border-radius-lg);
    }

    .cover-wrapper {
        height: 260px;
    }

    .profile-info {
        right: 16px;
        bottom: 16px;
        padding: 6px 10px 6px 6px;
    }

    .avatar {
        width: 70px;
        height: 70px;
        margin-right: 12px;
        border-width: 3px;
    }

    .username {
        font-size: 18px;
    }

    .post-form {
        margin: 16px 8px 12px;
        padding: 24px 20px;
        transform: translateY(0);
    }

    #post-content {
        height: 120px;
        padding: 16px;
        font-size: 15px;
    }

    /* 移动端评论表情选择器优化 */
    #moment-comment-emoji-picker {
        max-height: 280px;
        margin-top: 8px;
        border-radius: 12px;
        box-shadow:
            0 6px 24px rgba(0, 0, 0, 0.1),
            0 3px 12px rgba(0, 0, 0, 0.06);
        /* 确保有足够的宽度 */
        width: 100%;
        max-width: 400px;
        min-width: 320px;
    }

    .emoji-container {
        grid-template-columns: repeat(6, 1fr);
        gap: 10px;
        padding: 15px;
        max-height: 240px;
        /* 移动端滚动优化 */
        -webkit-overflow-scrolling: touch;
        overflow-scrolling: touch;
        scroll-behavior: smooth;
        /* 确保容器宽度足够 */
        width: 100%;
        box-sizing: border-box;
    }

    /* 移动端隐藏滚动条但保持滚动功能 */
    .emoji-container::-webkit-scrollbar {
        width: 0px;
        background: transparent;
    }

    .emoji {
        width: 40px;
        height: 40px;
        font-size: 24px;
        border-radius: 8px;
        /* 优化触摸体验 */
        min-height: 44px; /* 确保触摸目标足够大 */
        min-width: 44px;
        /* 添加触摸反馈 */
        -webkit-tap-highlight-color: rgba(255, 193, 7, 0.2);
        tap-highlight-color: rgba(255, 193, 7, 0.2);
        /* 确保表情居中显示 */
        display: flex;
        align-items: center;
        justify-content: center;
    }

    /* 优化评论弹窗在移动端的显示 */
    .comment-container {
        width: 95%;
        padding: 20px;
        margin: 0 10px;
    }

    #moment-comment-content {
        height: 80px;
        font-size: 15px;
        padding: 10px;
    }

    .emoji-btn {
        width: 44px;
        height: 44px;
        font-size: 20px;
        border-radius: 12px;
    }
}

@media (max-width: 480px) {
    .user-profile {
        height: 320px;
        margin: 0;
        border-radius: 0;
    }

    .cover-wrapper {
        height: 220px;
    }

    .profile-info {
        right: 12px;
        bottom: 12px;
        padding: 4px 8px 4px 4px;
        border-radius: var(--border-radius-md);
    }

    .avatar {
        width: 60px;
        height: 60px;
        margin-right: 10px;
        border-width: 2px;
    }

    .username {
        font-size: 16px;
        letter-spacing: 0.3px;
    }

    .post-form {
        margin: 12px 8px 0;
        padding: 20px 16px;
        transform: translateY(0);
        border-radius: var(--border-radius-lg);
    }

    #post-content {
        height: 100px;
        padding: 14px;
        font-size: 14px;
        border-radius: var(--border-radius-md);
    }

    .cover-container {
        margin: 16px;
        min-width: auto;
        max-width: none;
        padding: 20px;
    }

    .cover-upload-btn {
        padding: 14px 16px;
        font-size: 15px;
    }

    /* 小屏幕设备的表情选择器进一步优化 */
    #moment-comment-emoji-picker {
        max-height: 260px;
        margin-top: 6px;
        border-radius: 10px;
        /* 简化阴影以提升性能 */
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
        /* 确保小屏幕也有足够宽度 */
        width: 100%;
        max-width: 350px;
        min-width: 280px;
    }

    .emoji-container {
        grid-template-columns: repeat(6, 1fr);
        gap: 8px;
        padding: 12px;
        max-height: 220px;
        /* 移动端滚动优化 */
        -webkit-overflow-scrolling: touch;
        overflow-scrolling: touch;
        scroll-behavior: smooth;
        /* 完全隐藏滚动条 */
        scrollbar-width: none;
        -ms-overflow-style: none;
        /* 确保容器宽度足够 */
        width: 100%;
        box-sizing: border-box;
    }

    /* 确保滚动条完全隐藏 */
    .emoji-container::-webkit-scrollbar {
        display: none;
        width: 0;
        height: 0;
    }

    .emoji {
        width: 36px;
        height: 36px;
        font-size: 22px;
        border-radius: 6px;
        /* 确保触摸目标足够大 */
        min-height: 44px;
        min-width: 44px;
        /* 简化悬浮效果以提升性能 */
        transition: transform 0.2s ease, background 0.2s ease;
        /* 优化触摸反馈 */
        -webkit-tap-highlight-color: rgba(255, 193, 7, 0.3);
        tap-highlight-color: rgba(255, 193, 7, 0.3);
        /* 确保表情居中显示 */
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .emoji:hover {
        transform: scale(1.1);
        background: linear-gradient(135deg, #fff8e1 0%, #ffecb3 100%);
        /* 简化阴影 */
        box-shadow: 0 4px 12px rgba(243, 156, 18, 0.15);
    }

    /* 进一步优化评论弹窗 */
    .comment-container {
        width: 98%;
        padding: 16px;
        margin: 0 4px;
        border-radius: 12px;
    }

    #moment-comment-content {
        height: 70px;
        font-size: 14px;
        padding: 8px;
        border-radius: 6px;
    }

    .emoji-btn {
        width: 40px;
        height: 40px;
        font-size: 18px;
        border-radius: 10px;
    }

    /* 优化评论操作按钮 */
    .comment-btns button {
        padding: 8px 16px;
        font-size: 14px;
        border-radius: 8px;
    }
}

/* 高分辨率屏幕优化 */
@media (min-width: 1200px) {
    .user-profile {
        height: 349px;
    }

    .cover-wrapper {
        height: 350px;
    }

    .profile-info {
        right: 24px;
        bottom: 5px;
        padding: 10px 16px 10px 10px;
    }

    .avatar {
        width: 90px;
        height: 90px;
        margin-right: 18px;
        border-width: 5px;
    }

    .username {
        font-size: 22px;
        letter-spacing: 0.8px;
    }

    .post-form {
        margin: 24px 24px 20px;
        padding: 36px 28px;
        transform: translateY(0);
    }

    #post-content {
        height: 160px;
        padding: 24px;
        font-size: 17px;
    }
}
