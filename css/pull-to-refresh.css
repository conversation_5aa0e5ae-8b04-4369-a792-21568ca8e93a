/* 高端下拉刷新样式 - 大厂风格 */
.pull-refresh-indicator {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.95) 0%,
        rgba(248, 250, 252, 0.95) 30%,
        rgba(241, 245, 249, 0.95) 70%,
        rgba(236, 242, 249, 0.95) 100%);
    backdrop-filter: blur(40px) saturate(200%);
    -webkit-backdrop-filter: blur(40px) saturate(200%);
    border-bottom: 1px solid rgba(59, 130, 246, 0.1);
    transform: translateY(-80px);
    opacity: 0;
    transition: all 0.5s cubic-bezier(0.16, 1, 0.3, 1);
    z-index: 1000;
    box-shadow:
        0 20px 60px rgba(59, 130, 246, 0.08),
        0 10px 30px rgba(59, 130, 246, 0.06),
        0 4px 16px rgba(59, 130, 246, 0.04),
        0 2px 8px rgba(0, 0, 0, 0.02);
    border-radius: 0 0 20px 20px;
    overflow: hidden;
}

.refresh-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;
    color: #1e293b;
    font-size: 14px;
    font-weight: 600;
    letter-spacing: 0.3px;
    position: relative;
}

.refresh-content::before {
    content: '';
    position: absolute;
    top: -40px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 4px;
    background: linear-gradient(90deg,
        transparent 0%,
        rgba(59, 130, 246, 0.3) 20%,
        rgba(59, 130, 246, 0.8) 50%,
        rgba(59, 130, 246, 0.3) 80%,
        transparent 100%);
    border-radius: 2px;
    opacity: 0.6;
}

.refresh-icon {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background: linear-gradient(135deg,
        #3b82f6 0%,
        #2563eb 50%,
        #1d4ed8 100%);
    color: white;
    transition: all 0.4s cubic-bezier(0.16, 1, 0.3, 1);
    box-shadow:
        0 8px 25px rgba(59, 130, 246, 0.3),
        0 4px 12px rgba(59, 130, 246, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    position: relative;
    overflow: hidden;
}

.refresh-icon::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg,
        transparent 30%,
        rgba(255, 255, 255, 0.1) 50%,
        transparent 70%);
    transform: rotate(-45deg);
    transition: transform 0.6s ease;
}

.refresh-icon i {
    font-size: 14px;
    transition: transform 0.4s cubic-bezier(0.16, 1, 0.3, 1);
    position: relative;
    z-index: 1;
}

.refresh-text {
    transition: all 0.4s cubic-bezier(0.16, 1, 0.3, 1);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* 准备刷新状态 */
.pull-refresh-indicator.ready .refresh-icon {
    background: linear-gradient(135deg,
        #10b981 0%,
        #059669 50%,
        #047857 100%);
    transform: scale(1.15);
    box-shadow:
        0 12px 35px rgba(16, 185, 129, 0.4),
        0 6px 18px rgba(16, 185, 129, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.pull-refresh-indicator.ready .refresh-icon::before {
    transform: rotate(-45deg) translateX(100%);
}

.pull-refresh-indicator.ready .refresh-text {
    color: #059669;
    transform: scale(1.05);
}

/* 加载状态 */
.pull-refresh-indicator.loading .refresh-icon {
    background: linear-gradient(135deg,
        #f59e0b 0%,
        #d97706 50%,
        #b45309 100%);
    animation: loadingPulse 1.5s ease-in-out infinite;
    box-shadow:
        0 10px 30px rgba(245, 158, 11, 0.3),
        0 5px 15px rgba(245, 158, 11, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.pull-refresh-indicator.loading .refresh-text {
    color: #d97706;
    animation: textPulse 1.5s ease-in-out infinite;
}

@keyframes loadingPulse {
    0%, 100% {
        transform: scale(1);
        box-shadow:
            0 10px 30px rgba(245, 158, 11, 0.3),
            0 5px 15px rgba(245, 158, 11, 0.2);
    }
    50% {
        transform: scale(1.1);
        box-shadow:
            0 15px 40px rgba(245, 158, 11, 0.4),
            0 8px 20px rgba(245, 158, 11, 0.3);
    }
}

@keyframes textPulse {
    0%, 100% {
        opacity: 0.8;
        transform: scale(1);
    }
    50% {
        opacity: 1;
        transform: scale(1.02);
    }
}

/* 移动端优化 */
@media (max-width: 768px) {
    .pull-refresh-indicator {
        height: 70px;
        transform: translateY(-70px);
        border-radius: 0 0 16px 16px;
    }

    .refresh-content {
        font-size: 13px;
        gap: 10px;
    }

    .refresh-content::before {
        top: -35px;
        width: 50px;
        height: 3px;
    }

    .refresh-icon {
        width: 28px;
        height: 28px;
    }

    .refresh-icon i {
        font-size: 12px;
    }

    /* 移动端触觉反馈效果 */
    .pull-refresh-indicator.ready {
        animation: mobileReadyVibrate 0.1s ease-in-out;
    }
}

@keyframes mobileReadyVibrate {
    0%, 100% { transform: translateY(0) scale(1); }
    50% { transform: translateY(-2px) scale(1.02); }
}

/* 页面容器需要相对定位 */
.page {
    position: relative;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
}

/* 确保下拉刷新在正确的容器中工作 */
.page.active {
    position: relative;
}

/* 朋友圈页面特殊处理 */
#moments-page {
    position: relative;
}

#moments-page .pull-refresh-indicator {
    background: linear-gradient(135deg, 
        rgba(255, 255, 255, 0.98) 0%, 
        rgba(248, 250, 252, 0.98) 100%);
}

/* 聊天页面特殊处理 */
#chat-page {
    position: relative;
}

#chat-page .pull-refresh-indicator {
    background: linear-gradient(135deg, 
        rgba(255, 255, 255, 0.95) 0%, 
        rgba(248, 250, 252, 0.95) 100%);
}

/* 联系人页面特殊处理 */
#contacts-page {
    position: relative;
}

#contacts-page .pull-refresh-indicator {
    background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.95) 0%,
        rgba(248, 250, 252, 0.95) 30%,
        rgba(241, 245, 249, 0.95) 70%,
        rgba(236, 242, 249, 0.95) 100%);
}

/* 我页面特殊处理 */
#profile-page {
    position: relative;
}

#profile-page .pull-refresh-indicator {
    background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.95) 0%,
        rgba(252, 248, 255, 0.95) 30%,
        rgba(248, 245, 255, 0.95) 70%,
        rgba(243, 240, 255, 0.95) 100%);
    border-bottom: 1px solid rgba(139, 92, 246, 0.1);
}

#profile-page .refresh-icon {
    background: linear-gradient(135deg,
        #8b5cf6 0%,
        #7c3aed 50%,
        #6d28d9 100%);
    box-shadow:
        0 8px 25px rgba(139, 92, 246, 0.3),
        0 4px 12px rgba(139, 92, 246, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

#profile-page .pull-refresh-indicator.ready .refresh-icon {
    background: linear-gradient(135deg,
        #10b981 0%,
        #059669 50%,
        #047857 100%);
    box-shadow:
        0 12px 35px rgba(16, 185, 129, 0.4),
        0 6px 18px rgba(16, 185, 129, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

/* 确保刷新指示器不影响页面布局 */
.page-header {
    position: relative;
    z-index: 10;
}

.contacts-header-modern {
    position: relative;
    z-index: 10;
}

/* 动画效果 */
@keyframes pullRefreshBounce {
    0% {
        transform: translateY(-80px) scale(0.8);
        opacity: 0;
    }
    30% {
        transform: translateY(-20px) scale(1.1);
        opacity: 0.6;
    }
    60% {
        transform: translateY(-5px) scale(1.05);
        opacity: 0.9;
    }
    100% {
        transform: translateY(0px) scale(1);
        opacity: 1;
    }
}

@keyframes shimmerEffect {
    0% {
        background-position: -200% 0;
    }
    100% {
        background-position: 200% 0;
    }
}

.pull-refresh-indicator.loading {
    animation: pullRefreshBounce 0.6s cubic-bezier(0.16, 1, 0.3, 1);
}

.pull-refresh-indicator.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
        transparent 0%,
        rgba(255, 255, 255, 0.2) 50%,
        transparent 100%);
    animation: shimmerEffect 2s ease-in-out infinite;
}

/* 确保在所有设备上都能正常工作 */
.pull-refresh-indicator {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    -webkit-touch-callout: none;
    -webkit-tap-highlight-color: transparent;
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
    .pull-refresh-indicator {
        background: linear-gradient(135deg, 
            rgba(30, 41, 59, 0.95) 0%, 
            rgba(15, 23, 42, 0.95) 100%);
        border-bottom-color: rgba(71, 85, 105, 0.8);
    }
    
    .refresh-content {
        color: #94a3b8;
    }
}
