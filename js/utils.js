// 实用工具函数集合

// 消息搜索功能
class MessageSearch {
    constructor() {
        this.searchResults = [];
        this.currentIndex = 0;
    }

    // 搜索消息
    searchMessages(keyword, chatId = null) {
        if (!keyword.trim()) {
            this.clearSearch();
            return [];
        }

        const messages = document.querySelectorAll('.message');
        this.searchResults = [];

        messages.forEach((messageElement, index) => {
            const content = messageElement.querySelector('.message-bubble')?.textContent || '';
            const chatIdAttr = messageElement.closest('.chat-detail')?.dataset.chatId;
            
            // 如果指定了chatId，只搜索该聊天的消息
            if (chatId && chatIdAttr !== chatId.toString()) {
                return;
            }

            if (content.toLowerCase().includes(keyword.toLowerCase())) {
                this.searchResults.push({
                    element: messageElement,
                    index: index,
                    content: content
                });
            }
        });

        this.highlightSearchResults(keyword);
        return this.searchResults;
    }

    // 高亮搜索结果
    highlightSearchResults(keyword) {
        // 清除之前的高亮
        this.clearHighlight();

        this.searchResults.forEach(result => {
            const bubble = result.element.querySelector('.message-bubble');
            if (bubble) {
                const content = bubble.textContent;
                const regex = new RegExp(`(${keyword})`, 'gi');
                const highlightedContent = content.replace(regex, '<mark>$1</mark>');
                bubble.innerHTML = highlightedContent;
            }
        });
    }

    // 清除高亮
    clearHighlight() {
        document.querySelectorAll('.message-bubble mark').forEach(mark => {
            const parent = mark.parentNode;
            parent.replaceChild(document.createTextNode(mark.textContent), mark);
            parent.normalize();
        });
    }

    // 跳转到下一个搜索结果
    nextResult() {
        if (this.searchResults.length === 0) return;

        this.currentIndex = (this.currentIndex + 1) % this.searchResults.length;
        this.scrollToResult();
    }

    // 跳转到上一个搜索结果
    prevResult() {
        if (this.searchResults.length === 0) return;

        this.currentIndex = (this.currentIndex - 1 + this.searchResults.length) % this.searchResults.length;
        this.scrollToResult();
    }

    // 滚动到当前搜索结果
    scrollToResult() {
        if (this.searchResults.length === 0) return;

        const result = this.searchResults[this.currentIndex];
        result.element.scrollIntoView({ behavior: 'smooth', block: 'center' });
        
        // 添加临时高亮效果
        result.element.classList.add('search-highlight');
        setTimeout(() => {
            result.element.classList.remove('search-highlight');
        }, 2000);
    }

    // 清除搜索
    clearSearch() {
        this.clearHighlight();
        this.searchResults = [];
        this.currentIndex = 0;
    }
}

// 聊天记录导出功能
class ChatExporter {
    // 导出聊天记录为文本
    exportToText(chatId, chatName) {
        const messages = this.getChatMessages(chatId);
        if (messages.length === 0) {
            showToast('没有聊天记录可导出', 'info');
            return;
        }

        let textContent = `聊天记录 - ${chatName}\n`;
        textContent += `导出时间: ${new Date().toLocaleString()}\n`;
        textContent += '=' .repeat(50) + '\n\n';

        messages.forEach(msg => {
            const time = msg.time || '';
            const sender = msg.type === 'sent' ? '我' : chatName;
            const content = msg.content || '';
            
            textContent += `[${time}] ${sender}: ${content}\n`;
        });

        this.downloadFile(`${chatName}_聊天记录.txt`, textContent, 'text/plain');
    }

    // 导出聊天记录为JSON
    exportToJSON(chatId, chatName) {
        const messages = this.getChatMessages(chatId);
        if (messages.length === 0) {
            showToast('没有聊天记录可导出', 'info');
            return;
        }

        const exportData = {
            chatName: chatName,
            chatId: chatId,
            exportTime: new Date().toISOString(),
            messageCount: messages.length,
            messages: messages
        };

        const jsonContent = JSON.stringify(exportData, null, 2);
        this.downloadFile(`${chatName}_聊天记录.json`, jsonContent, 'application/json');
    }

    // 获取聊天消息数据
    getChatMessages(chatId) {
        const messages = [];
        const messageElements = document.querySelectorAll('.message');

        messageElements.forEach(element => {
            const type = element.classList.contains('sent') ? 'sent' : 'received';
            const content = element.querySelector('.message-bubble')?.textContent || '';
            const time = element.querySelector('.message-time')?.textContent || '';

            if (content) {
                messages.push({
                    type: type,
                    content: content,
                    time: time,
                    timestamp: Date.now()
                });
            }
        });

        return messages;
    }

    // 下载文件
    downloadFile(filename, content, mimeType) {
        const blob = new Blob([content], { type: mimeType });
        const url = URL.createObjectURL(blob);
        
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        
        URL.revokeObjectURL(url);
        showToast('聊天记录导出成功', 'success');
    }
}

// 通知管理器
class NotificationManager {
    constructor() {
        this.permission = 'default';
        this.init();
    }

    // 初始化通知权限
    async init() {
        if ('Notification' in window) {
            this.permission = await Notification.requestPermission();
        }
    }

    // 显示桌面通知
    showNotification(title, options = {}) {
        if (this.permission !== 'granted') {
            return;
        }

        const defaultOptions = {
            icon: '/favicon.ico',
            badge: '/favicon.ico',
            tag: 'wechat-message',
            requireInteraction: false,
            silent: false
        };

        const notification = new Notification(title, { ...defaultOptions, ...options });
        
        // 自动关闭通知
        setTimeout(() => {
            notification.close();
        }, 5000);

        return notification;
    }

    // 显示新消息通知
    showMessageNotification(senderName, message, avatar) {
        if (document.hasFocus()) {
            // 如果页面处于焦点状态，不显示桌面通知
            return;
        }

        this.showNotification(`${senderName} 发来消息`, {
            body: message,
            icon: avatar || '/favicon.ico',
            tag: `message-${senderName}`,
            data: { senderName, message }
        });
    }
}

// 聊天统计功能
class ChatStatistics {
    // 获取聊天统计信息
    getChatStats(chatId) {
        const messages = document.querySelectorAll('.message');
        let sentCount = 0;
        let receivedCount = 0;
        let totalChars = 0;
        let mediaCount = 0;

        messages.forEach(element => {
            const content = element.querySelector('.message-bubble')?.textContent || '';
            const isMedia = element.querySelector('.message-image, .message-video');

            if (element.classList.contains('sent')) {
                sentCount++;
            } else {
                receivedCount++;
            }

            if (isMedia) {
                mediaCount++;
            } else {
                totalChars += content.length;
            }
        });

        return {
            totalMessages: sentCount + receivedCount,
            sentMessages: sentCount,
            receivedMessages: receivedCount,
            totalCharacters: totalChars,
            mediaMessages: mediaCount,
            averageMessageLength: totalChars / (sentCount + receivedCount - mediaCount) || 0
        };
    }

    // 显示统计信息
    showStats(chatId, chatName) {
        const stats = this.getChatStats(chatId);
        
        const statsHtml = `
            <div class="chat-stats-modal">
                <div class="stats-container">
                    <div class="stats-header">
                        <h3>${chatName} - 聊天统计</h3>
                        <button class="close-stats-btn"><i class="fas fa-times"></i></button>
                    </div>
                    <div class="stats-content">
                        <div class="stat-item">
                            <span class="stat-label">总消息数</span>
                            <span class="stat-value">${stats.totalMessages}</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">发送消息</span>
                            <span class="stat-value">${stats.sentMessages}</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">接收消息</span>
                            <span class="stat-value">${stats.receivedMessages}</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">媒体消息</span>
                            <span class="stat-value">${stats.mediaMessages}</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">总字符数</span>
                            <span class="stat-value">${stats.totalCharacters}</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">平均消息长度</span>
                            <span class="stat-value">${Math.round(stats.averageMessageLength)}</span>
                        </div>
                    </div>
                </div>
            </div>
        `;

        const modalDiv = document.createElement('div');
        modalDiv.innerHTML = statsHtml;
        document.body.appendChild(modalDiv);

        // 绑定关闭事件
        modalDiv.querySelector('.close-stats-btn').addEventListener('click', () => {
            modalDiv.remove();
        });

        modalDiv.addEventListener('click', (e) => {
            if (e.target === modalDiv) {
                modalDiv.remove();
            }
        });
    }
}

// 快捷键管理器
class KeyboardShortcuts {
    constructor() {
        this.shortcuts = new Map();
        this.init();
    }

    init() {
        document.addEventListener('keydown', (e) => {
            const key = this.getKeyString(e);
            const handler = this.shortcuts.get(key);
            
            if (handler) {
                e.preventDefault();
                handler(e);
            }
        });

        // 注册默认快捷键
        this.registerDefaults();
    }

    // 获取按键字符串
    getKeyString(e) {
        const parts = [];
        if (e.ctrlKey) parts.push('Ctrl');
        if (e.altKey) parts.push('Alt');
        if (e.shiftKey) parts.push('Shift');
        if (e.metaKey) parts.push('Meta');
        parts.push(e.key);
        return parts.join('+');
    }

    // 注册快捷键
    register(keyString, handler) {
        this.shortcuts.set(keyString, handler);
    }

    // 注册默认快捷键
    registerDefaults() {
        // Ctrl+F: 搜索消息
        this.register('Ctrl+f', () => {
            this.showSearchDialog();
        });

        // Ctrl+E: 导出聊天记录
        this.register('Ctrl+e', () => {
            if (currentChatId) {
                const chat = chats.find(c => c.id === currentChatId);
                if (chat) {
                    const exporter = new ChatExporter();
                    exporter.exportToText(currentChatId, chat.name);
                }
            }
        });

        // Ctrl+I: 显示聊天统计
        this.register('Ctrl+i', () => {
            if (currentChatId) {
                const chat = chats.find(c => c.id === currentChatId);
                if (chat) {
                    const stats = new ChatStatistics();
                    stats.showStats(currentChatId, chat.name);
                }
            }
        });

        // Escape: 关闭当前对话框
        this.register('Escape', () => {
            const modals = document.querySelectorAll('.modal, .chat-stats-modal, .search-dialog');
            modals.forEach(modal => modal.remove());
        });
    }

    // 显示搜索对话框
    showSearchDialog() {
        if (!currentChatId) {
            showToast('请先打开一个聊天', 'info');
            return;
        }

        const searchHtml = `
            <div class="search-dialog">
                <div class="search-container">
                    <div class="search-header">
                        <h3>搜索消息</h3>
                        <button class="close-search-btn"><i class="fas fa-times"></i></button>
                    </div>
                    <div class="search-input-container">
                        <input type="text" id="message-search-input" placeholder="输入关键词搜索消息...">
                        <div class="search-controls">
                            <button id="search-prev" class="search-nav-btn" disabled><i class="fas fa-chevron-up"></i></button>
                            <span id="search-counter">0/0</span>
                            <button id="search-next" class="search-nav-btn" disabled><i class="fas fa-chevron-down"></i></button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        const searchDiv = document.createElement('div');
        searchDiv.innerHTML = searchHtml;
        document.body.appendChild(searchDiv);

        const searchInput = document.getElementById('message-search-input');
        const searchCounter = document.getElementById('search-counter');
        const prevBtn = document.getElementById('search-prev');
        const nextBtn = document.getElementById('search-next');
        const messageSearch = new MessageSearch();

        // 搜索输入事件
        searchInput.addEventListener('input', (e) => {
            const keyword = e.target.value.trim();
            const results = messageSearch.searchMessages(keyword, currentChatId);
            
            searchCounter.textContent = `${results.length > 0 ? messageSearch.currentIndex + 1 : 0}/${results.length}`;
            prevBtn.disabled = results.length === 0;
            nextBtn.disabled = results.length === 0;

            if (results.length > 0) {
                messageSearch.scrollToResult();
            }
        });

        // 导航按钮事件
        prevBtn.addEventListener('click', () => {
            messageSearch.prevResult();
            searchCounter.textContent = `${messageSearch.currentIndex + 1}/${messageSearch.searchResults.length}`;
        });

        nextBtn.addEventListener('click', () => {
            messageSearch.nextResult();
            searchCounter.textContent = `${messageSearch.currentIndex + 1}/${messageSearch.searchResults.length}`;
        });

        // 关闭事件
        searchDiv.querySelector('.close-search-btn').addEventListener('click', () => {
            messageSearch.clearSearch();
            searchDiv.remove();
        });

        // 自动聚焦搜索框
        setTimeout(() => searchInput.focus(), 100);
    }
}

// 初始化工具类
const messageSearch = new MessageSearch();
const chatExporter = new ChatExporter();
const notificationManager = new NotificationManager();
const chatStatistics = new ChatStatistics();
const keyboardShortcuts = new KeyboardShortcuts();

// 导出到全局作用域
window.MessageSearch = MessageSearch;
window.ChatExporter = ChatExporter;
window.NotificationManager = NotificationManager;
window.ChatStatistics = ChatStatistics;
window.KeyboardShortcuts = KeyboardShortcuts;

// 导出实例
window.messageSearch = messageSearch;
window.chatExporter = chatExporter;
window.notificationManager = notificationManager;
window.chatStatistics = chatStatistics;
window.keyboardShortcuts = keyboardShortcuts;