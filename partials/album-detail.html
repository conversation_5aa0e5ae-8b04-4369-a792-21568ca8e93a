<div class="page-header">
    <button class="back-btn" onclick="showPage('albums')">
        <i class="fas fa-arrow-left"></i>
    </button>
    <h2 id="album-title">相册详情</h2>
    <div class="header-actions">
        <button class="edit-album-btn" id="edit-album-btn" title="编辑相册">
            <i class="fas fa-edit"></i>
        </button>
        <button class="delete-album-btn" id="delete-album-btn" title="删除相册">
            <i class="fas fa-trash"></i>
        </button>
    </div>
</div>

<div class="album-detail-content">
    <!-- 相册信息卡片 -->
    <div class="album-info">
        <div class="album-header">
            <h3 class="album-name" id="album-name-display">相册名称</h3>
            <div class="album-stats">
                <span id="media-count">0 张照片</span>
                <span id="album-date">创建于 --</span>
            </div>
        </div>
        <p class="album-description-text" id="album-description-text"></p>
    </div>
    
    <!-- 上传区域 -->
    <div class="upload-section">
        <div class="upload-area" id="upload-area">
            <div class="upload-content">
                <i class="fas fa-cloud-upload-alt"></i>
                <h3>上传照片或视频</h3>
                <p>支持 JPG、PNG、GIF、MP4 格式</p>
                <button class="upload-btn" onclick="document.getElementById('media-upload').click()">
                    选择文件
                </button>
                <input type="file" id="media-upload" multiple accept="image/*,video/*" hidden>
            </div>
            <div class="upload-progress" id="upload-progress" style="display: none;">
                <div class="progress-bar">
                    <div class="progress-fill" id="progress-fill"></div>
                </div>
                <span class="progress-text" id="progress-text">上传中... 0%</span>
            </div>
        </div>
    </div>
    
    <!-- 媒体网格 -->
    <div class="media-grid" id="media-grid">
        <!-- 媒体项将通过JavaScript动态生成 -->
    </div>
    
    <!-- 空状态 -->
    <div class="empty-media-state" id="empty-media" style="display: none;">
        <i class="fas fa-images"></i>
        <h3>相册是空的</h3>
        <p>上传你的第一张照片或视频</p>
    </div>
</div>

<!-- 媒体预览弹窗 -->
<div class="media-preview-modal" id="media-preview-modal">
    <div class="media-preview-container">
        <div class="media-preview-header">
            <button class="close-btn" onclick="hideMediaPreview()">
                <i class="fas fa-times"></i>
            </button>
            <button class="delete-media-btn" onclick="deleteCurrentMedia()">
                <i class="fas fa-trash"></i>
            </button>
        </div>
        <div class="media-preview-content">
            <div class="media-container" id="media-container">
                <!-- 媒体内容将动态插入 -->
            </div>
        </div>
        <div class="media-preview-footer">
            <button class="nav-btn prev-btn" onclick="showPrevMedia()">
                <i class="fas fa-chevron-left"></i>
            </button>
            <span class="media-counter" id="media-counter">1 / 1</span>
            <button class="nav-btn next-btn" onclick="showNextMedia()">
                <i class="fas fa-chevron-right"></i>
            </button>
        </div>
    </div>
</div>

<!-- 删除媒体确认弹窗 -->
<div class="confirm-modal" id="delete-media-confirm-modal">
    <div class="confirm-modal-container">
        <div class="confirm-modal-content">
            <i class="fas fa-exclamation-triangle"></i>
            <h3>确认删除</h3>
            <p>确定要删除这个文件吗？此操作无法撤销。</p>
            <div class="confirm-actions">
                <button class="cancel-btn" onclick="hideDeleteMediaConfirmModal()">取消</button>
                <button class="delete-btn" onclick="confirmDeleteMedia()">删除</button>
            </div>
        </div>
    </div>
</div>

<!-- 弹窗已移至主页面 index.html 中，避免重复定义 -->
