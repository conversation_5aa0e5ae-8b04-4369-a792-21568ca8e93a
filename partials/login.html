<div class="login-modal" id="login-modal">
  <div class="login-container">
    <div class="login-header">
      <h3 id="login-title">登录</h3>
      <button id="close-login-modal" class="close-btn"><i class="fas fa-times"></i></button>
    </div>
    <div class="login-form" id="login-form">
      <div class="form-group">
        <label for="login-username">用户名</label>
        <input type="text" id="login-username" placeholder="请输入用户名">
      </div>
      <div class="form-group">
        <label for="login-password">密码</label>
        <input type="password" id="login-password" placeholder="请输入密码">
      </div>
      <button id="login-btn" class="login-btn">登录</button>
      <div class="form-switch">
        没有账号？<span id="show-register">注册</span>
      </div>
    </div>
    <div class="login-form" id="register-form" style="display:none;">
      <div class="form-group">
        <label for="register-username">用户名</label>
        <input type="text" id="register-username" placeholder="请输入用户名">
      </div>
      <div class="form-group">
        <label for="register-password">密码</label>
        <input type="password" id="register-password" placeholder="请输入密码">
      </div>
      <div class="form-group">
        <label for="register-avatar">头像URL(可选)</label>
        <input type="text" id="register-avatar" placeholder="头像图片URL">
      </div>
      <button id="register-btn" class="login-btn">注册</button>
      <div class="form-switch">
        已有账号？<span id="show-login">登录</span>
      </div>
    </div>
    <div id="login-error" class="login-error"></div>
  </div>
</div>
<style>
.login-modal {
  position: fixed;
  top: 0; left: 0; right: 0; bottom: 0;
  background: rgba(0,0,0,0.4);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}
.login-container {
  background: #fff;
  border-radius: 10px;
  width: 320px;
  box-shadow: 0 2px 16px rgba(0,0,0,0.2);
  padding: 24px 24px 12px 24px;
  position: relative;
}
.login-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 10px;
}
.login-header h3 { margin: 0; font-size: 20px; }
.close-btn { background: none; border: none; font-size: 20px; color: #999; cursor: pointer; }
.form-group { margin-bottom: 16px; }
.form-group label { display: block; margin-bottom: 4px; color: #666; font-size: 14px; }
.form-group input { width: 100%; padding: 8px; border: 1px solid #eee; border-radius: 5px; font-size: 16px; }
.login-btn { width: 100%; padding: 10px; background: #07c160; color: #fff; border: none; border-radius: 5px; font-size: 16px; cursor: pointer; margin-bottom: 8px; }
.form-switch { text-align: right; font-size: 13px; color: #888; }
.form-switch span { color: #07c160; cursor: pointer; margin-left: 4px; }
.login-error { color: #e64340; text-align: center; margin-top: 8px; font-size: 14px; min-height: 20px; }
</style> 