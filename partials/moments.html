<!-- 用户信息区域 -->
<div class="user-profile">
    <div class="cover-wrapper" id="cover-wrapper">
        <!-- 封面区域 -->
        <div class="cover-image" id="cover-image"></div>
        <div class="cover-video-container" id="cover-video-container">
            <video class="cover-video" id="cover-video" muted loop></video>
        </div>
        <!-- 点击提示 -->
        <div class="click-hint">
            <i class="fas fa-camera"></i> 点击更换背景
        </div>
    </div>
    <div class="profile-info">
        <div class="username" id="user-name">用户名</div>
        <div class="avatar" id="user-avatar"></div>
    </div>
</div>

<!-- 发布朋友圈 -->
<div class="post-form">
    <textarea id="post-content" placeholder="分享新鲜事..."></textarea>
    
    <!-- 图片预览区域 -->
    <div class="preview-container" id="preview-container"></div>
    
    <div class="post-actions">
        <div class="media-actions">
            <div class="media-btn emoji-btn" id="post-emoji-btn" title="添加表情">
                <i class="fas fa-smile"></i>
            </div>
            <label for="image-upload" class="media-btn" title="上传图片">
                <i class="fas fa-image"></i>
            </label>
            <input type="file" id="image-upload" accept="image/*" multiple hidden>
            
            <label for="video-upload" class="media-btn" title="上传视频">
                <i class="fas fa-video"></i>
            </label>
            <input type="file" id="video-upload" accept="video/*" hidden>
        </div>
        <button id="publish-btn">发布</button>
    </div>
    
    <!-- 表情选择器 -->
    <div class="emoji-picker" id="post-emoji-picker">
        <div class="emoji-container">
            <div class="emoji">😀</div>
            <div class="emoji">😁</div>
            <div class="emoji">😂</div>
            <div class="emoji">🤣</div>
            <div class="emoji">😊</div>
            <div class="emoji">😍</div>
            <div class="emoji">🥰</div>
            <div class="emoji">😘</div>
            <div class="emoji">😗</div>
            <div class="emoji">😙</div>
            <div class="emoji">🤔</div>
            <div class="emoji">🤨</div>
            <div class="emoji">😏</div>
            <div class="emoji">😮</div>
            <div class="emoji">😱</div>
            <div class="emoji">😭</div>
            <div class="emoji">😤</div>
            <div class="emoji">🥳</div>
            <div class="emoji">👍</div>
            <div class="emoji">❤️</div>
            <div class="emoji">💕</div>
            <div class="emoji">🌹</div>
            <div class="emoji">🌞</div>
            <div class="emoji">🌈</div>
        </div>
    </div>
</div>

<!-- 朋友圈列表 -->
<div class="moments-list" id="moments-container">
    <!-- 朋友圈内容会通过JavaScript动态生成 -->
</div>

<!-- 评论弹窗 -->
<div class="comment-modal" id="moment-comment-modal">
    <div class="comment-container">
        <div id="moment-reply-info" class="reply-info">
            <span>回复: </span><span id="reply-user"></span>
            <button id="moment-cancel-reply"><i class="fas fa-times"></i></button>
        </div>
        <div class="comment-content-wrapper">
            <textarea id="moment-comment-content" placeholder="评论..."></textarea>
            <!-- 评论表情选择器 -->
            <div class="emoji-picker" id="moment-comment-emoji-picker">
                <div class="emoji-container">
                    <!-- 笑脸表情 -->
                    <div class="emoji">😀</div>
                    <div class="emoji">😁</div>
                    <div class="emoji">😂</div>
                    <div class="emoji">🤣</div>
                    <div class="emoji">😊</div>
                    <div class="emoji">😍</div>
                    <div class="emoji">🥰</div>
                    <div class="emoji">😘</div>
                    <div class="emoji">😗</div>
                    <div class="emoji">😙</div>
                    <div class="emoji">😚</div>
                    <div class="emoji">🤗</div>
                    <div class="emoji">🤔</div>
                    <div class="emoji">🤨</div>
                    <div class="emoji">😏</div>
                    <div class="emoji">😮</div>
                    <div class="emoji">😱</div>
                    <div class="emoji">😭</div>
                    <div class="emoji">😤</div>
                    <div class="emoji">🥳</div>
                    <div class="emoji">😎</div>
                    <div class="emoji">🤓</div>
                    <div class="emoji">🧐</div>
                    <div class="emoji">😴</div>
                    <!-- 手势表情 -->
                    <div class="emoji">👍</div>
                    <div class="emoji">👎</div>
                    <div class="emoji">👌</div>
                    <div class="emoji">✌️</div>
                    <div class="emoji">🤞</div>
                    <div class="emoji">🤟</div>
                    <div class="emoji">🤘</div>
                    <div class="emoji">👏</div>
                    <div class="emoji">🙌</div>
                    <div class="emoji">👐</div>
                    <div class="emoji">🤲</div>
                    <div class="emoji">🙏</div>
                    <!-- 爱心表情 -->
                    <div class="emoji">❤️</div>
                    <div class="emoji">💕</div>
                    <div class="emoji">💖</div>
                    <div class="emoji">💗</div>
                    <div class="emoji">💙</div>
                    <div class="emoji">💚</div>
                    <div class="emoji">💛</div>
                    <div class="emoji">🧡</div>
                    <div class="emoji">💜</div>
                    <div class="emoji">🖤</div>
                    <div class="emoji">🤍</div>
                    <div class="emoji">🤎</div>
                    <!-- 自然表情 -->
                    <div class="emoji">🌹</div>
                    <div class="emoji">🌸</div>
                    <div class="emoji">🌺</div>
                    <div class="emoji">🌻</div>
                    <div class="emoji">🌞</div>
                    <div class="emoji">🌝</div>
                    <div class="emoji">🌛</div>
                    <div class="emoji">⭐</div>
                    <div class="emoji">🌟</div>
                    <div class="emoji">✨</div>
                    <div class="emoji">🌈</div>
                    <div class="emoji">☀️</div>
                    <!-- 食物表情 -->
                    <div class="emoji">🍎</div>
                    <div class="emoji">🍊</div>
                    <div class="emoji">🍋</div>
                    <div class="emoji">🍌</div>
                    <div class="emoji">🍉</div>
                    <div class="emoji">🍇</div>
                    <div class="emoji">🍓</div>
                    <div class="emoji">🥝</div>
                    <div class="emoji">🍑</div>
                    <div class="emoji">🥭</div>
                    <div class="emoji">🍍</div>
                    <div class="emoji">🥥</div>
                </div>
            </div>
        </div>
        <div class="comment-actions">
            <div class="emoji-btn" id="moment-comment-emoji-btn">
                <i class="fas fa-smile"></i>
            </div>
            <div class="comment-btns">
                <button id="moment-cancel-comment">取消</button>
                <button id="moment-submit-comment">发送</button>
            </div>
        </div>
    </div>
</div>

<!-- 更换封面弹窗 -->
<div class="cover-modal" id="cover-modal">
    <div class="cover-container">
        <div class="cover-header">
            <h3>更换封面</h3>
            <button id="close-cover" class="close-btn"><i class="fas fa-times"></i></button>
        </div>
        <div class="cover-options">
            <div class="cover-option">
                <label for="cover-image-upload" class="cover-upload-btn">
                    <i class="fas fa-image"></i>
                    <span>上传图片</span>
                    <input type="file" id="cover-image-upload" accept="image/*" hidden>
                </label>
            </div>
            <div class="cover-option">
                <label for="cover-video-upload" class="cover-upload-btn">
                    <i class="fas fa-video"></i>
                    <span>上传视频</span>
                    <input type="file" id="cover-video-upload" accept="video/*" hidden>
                </label>
            </div>
        </div>
    </div>
</div> 